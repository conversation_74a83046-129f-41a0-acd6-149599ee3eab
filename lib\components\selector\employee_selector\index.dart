import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_provider.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree_model.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_dialog.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';

/// 员工选择器组件
class EmployeeSelector extends StatefulWidget {
  /// 提交按钮点击回调
  final void Function(List<Employee>)? onChange;

  /// 默认选中的员工对象列表(打开弹窗时默认选中)(适用于回显)
  final List<Employee>? defaultCheckedEmployeeList;

  /// 是否允许拖拽排序，默认为true
  final bool isDrag;

  /// 最多可见行数
  final int maxVisibleRows;

  /// 是否禁用组件，默认为false
  final bool disabled;

  /// 最大可选择员工数量，为null时表示无限制
  /// 当达到限制时，阻止进一步选择并显示适当的用户反馈
  final int? maxSelectableEmployees;

  /// 占位符文本
  final String? placeholder;

  /// 是否显示widget(通过方法打开时设置为false)
  final bool showWidget;

  /// 禁用选择的员工ID列表
  /// 当员工ID包含在此列表中时，该员工将无法被选择/取消选择
  final List<String>? disabledIdList;

  const EmployeeSelector({
    super.key,
    this.onChange,
    this.maxVisibleRows = 3,
    this.defaultCheckedEmployeeList,
    this.isDrag = true,
    this.disabled = false,
    this.maxSelectableEmployees,
    this.placeholder,
    this.showWidget = true,
    this.disabledIdList,
  });

  @override
  State<EmployeeSelector> createState() => EmployeeSelectorState();
}

class EmployeeSelectorState extends State<EmployeeSelector> {
  // 创建 GlobalKey 用于访问 EmployeeTree 的方法
  final GlobalKey<EmployeeTreeState> _employeeSelectorKey = GlobalKey<EmployeeTreeState>();

  // 维护当前选中的员工列表（内部使用 EmployeeTreeModel）
  List<EmployeeTreeModel> _selectedEmployees = [];

  @override
  void initState() {
    super.initState();
    // 如果有默认选中的员工对象列表，转换为 EmployeeTreeModel 并初始化到本地状态
    if (widget.defaultCheckedEmployeeList != null &&
        widget.defaultCheckedEmployeeList!.isNotEmpty) {
      _selectedEmployees = Employee.toEmployeeTreeModelList(widget.defaultCheckedEmployeeList!);
    }
  }

  @override
  void didUpdateWidget(EmployeeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 监听 defaultCheckedEmployeeList 的变化
    if (widget.defaultCheckedEmployeeList != null && widget.defaultCheckedEmployeeList!.isEmpty) {
      _selectedEmployees = [];
    }
  }

  /// 打开选择员工弹窗
  void showEmployeeSelectorDialog(BuildContext context) {
    // 如果组件被禁用，不允许打开弹窗
    if (widget.disabled) {
      return;
    }

    late EmployeeSelectorProvider provider;

    AppDialog.show(
      width: 800,
      height: 600,
      padding: EdgeInsetsGeometry.zero,
      context: context,
      title: '选择员工',
      isDrawer: false,
      child: ChangeNotifierProvider(
        create: (_) {
          provider = EmployeeSelectorProvider();
          // 设置默认选中的员工对象列表，转换为ID列表传递给Provider
          if (widget.defaultCheckedEmployeeList != null &&
              widget.defaultCheckedEmployeeList!.isNotEmpty) {
            final employeeIds =
                widget.defaultCheckedEmployeeList!
                    .where((employee) => employee.employeeId != null)
                    .map((employee) => employee.employeeId!)
                    .toList();
            provider.setDefaultCheckedEmployeeIds(employeeIds);
          }
          // 设置最大选择限制
          if (widget.maxSelectableEmployees != null) {
            provider.setMaxSelectableEmployees(widget.maxSelectableEmployees!);
          }
          // 设置禁用选择的员工ID列表
          if (widget.disabledIdList != null) {
            provider.setDisabledIdList(widget.disabledIdList!);
          }
          return provider;
        },
        child: EmployeeSelectorDialog(employeeSelectorKey: _employeeSelectorKey),
      ),
      onConfirm: () {
        // 检查是否超过最大选择限制
        if (widget.maxSelectableEmployees != null &&
            provider.checkedEmployees.length > widget.maxSelectableEmployees!) {
          ToastManager.error('最多只能选择 ${widget.maxSelectableEmployees} 名员工');
          return;
        }

        // 更新本地选中状态
        setState(() {
          _selectedEmployees = List.from(provider.checkedEmployees);
        });
        // 转换为 Employee 列表后回调
        final employeeList = Employee.fromEmployeeTreeModelList(provider.checkedEmployees);
        widget.onChange?.call(employeeList);
        context.pop();
      },
    );
  }

  /// 计算员工选择器的动态高度
  double _calculateSelectorHeight({
    required List<EmployeeTreeModel> selectedEmployees, // 已选择的员工列表
    required int maxVisibleRows, // 最多可见行数，超过此数量将显示滚动条
    required double itemHeight, // 每个员工项的高度
    required double selectorPadding, // 选择器容器的内边距
    required double itemBottomMargin, // 每个员工项的底部外边距
  }) {
    // 基础高度：单项高度 + 上下内边距
    double baseHeight = itemHeight + selectorPadding * 2;

    // 如果没有选择员工或只选择了1个员工，返回基础高度
    if (selectedEmployees.length <= 1) {
      return baseHeight;
    }

    // 计算实际显示的行数（不超过最大可见行数）
    int actualVisibleRows =
        selectedEmployees.length > maxVisibleRows ? maxVisibleRows : selectedEmployees.length;

    // 计算多行情况下的总高度
    // 公式：单项高度 * 行数 + 上下内边距 * 2 + 每行底部外边距 * 行数
    return itemHeight * actualVisibleRows +
        selectorPadding * 2 +
        itemBottomMargin * actualVisibleRows;
  }

  /// 构建员工项
  Widget _buildEmployeeItem(EmployeeTreeModel employee, double itemBottomMargin, {int? index}) {
    final isEmployeeDisabled = widget.disabledIdList?.contains(employee.id) ?? false;
    final isItemDisabled = widget.disabled || isEmployeeDisabled;

    return Container(
      key:
          widget.isDrag && !isItemDisabled && _selectedEmployees.length > 1
              ? ValueKey(employee.id)
              : null,
      padding: const EdgeInsets.all(5),
      margin: EdgeInsets.only(right: 10, bottom: itemBottomMargin),
      decoration: BoxDecoration(
        color: isItemDisabled ? context.background100 : context.background200,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 只有在允许拖拽且未禁用且有多个项目时才显示拖拽图标
          if (widget.isDrag && !isItemDisabled && index != null && _selectedEmployees.length > 1)
            ReorderableDragStartListener(
              index: index,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Icon(
                  IconFont.xianxing_tuodongpaixu,
                  color: AppColors.icon200,
                  size: AppIconSize.small,
                ),
              ),
            ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(bottom: 2, left: 8),
              child: Text(
                employee.name,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: isItemDisabled ? context.textSecondary : null),
              ),
            ),
          ),
          // 只有在未禁用且员工不在禁用列表中时才显示删除按钮
          if (!isItemDisabled)
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedEmployees.removeWhere((e) => e.id == employee.id);
                    // 转换为 Employee 列表后回调
                    final employeeList = Employee.fromEmployeeTreeModelList(_selectedEmployees);
                    widget.onChange?.call(employeeList);
                  });
                },
                child: Icon(
                  IconFont.mianxing_guanbi,
                  color: context.icon200,
                  size: AppIconSize.small,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    if (widget.placeholder == null) {
      return const SizedBox(); // 不显示任何内容
    }
    return Center(
      child: Text(
        widget.placeholder!,
        style: TextStyle(color: context.textSecondary, fontSize: 14),
      ),
    );
  }

  /// 构建选择器
  @override
  Widget build(BuildContext context) {
    /// 选择器内边距
    double selectorPadding = 4;

    /// 每项的高度
    double itemHeight = 32;

    /// 每项的底部外边距
    double itemBottomMargin = 5;

    /// 使用提取的方法计算选择器动态高度
    double selectorHeight = _calculateSelectorHeight(
      selectedEmployees: _selectedEmployees,
      maxVisibleRows: widget.maxVisibleRows,
      itemHeight: itemHeight,
      selectorPadding: selectorPadding,
      itemBottomMargin: itemBottomMargin,
    );

    return widget.showWidget
        ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              height: selectorHeight,
              padding: EdgeInsets.all(selectorPadding),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                border: Border.all(color: widget.disabled ? context.border200 : context.border300),
                color: widget.disabled ? context.background100 : null,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  /// 已选员工列表或占位符
                  Expanded(
                    child:
                        _selectedEmployees.isEmpty
                            ? _buildPlaceholder()
                            : widget.isDrag && !widget.disabled && _selectedEmployees.length > 1
                            ? ReorderableListView(
                              shrinkWrap: true,
                              buildDefaultDragHandles: false,
                              children:
                                  _selectedEmployees.asMap().entries.map((entry) {
                                    final index = entry.key;
                                    final employee = entry.value;
                                    return _buildEmployeeItem(
                                      employee,
                                      itemBottomMargin,
                                      index: index,
                                    );
                                  }).toList(),
                              onReorder: (oldIndex, newIndex) {
                                if (widget.disabled) return; // 禁用时不允许拖拽

                                // 检查被拖拽的员工是否被禁用
                                final draggedEmployee = _selectedEmployees[oldIndex];
                                if (widget.disabledIdList?.contains(draggedEmployee.id) ?? false) {
                                  return; // 禁用的员工不允许拖拽
                                }

                                setState(() {
                                  if (oldIndex < newIndex) {
                                    newIndex -= 1;
                                  }
                                  final employee = _selectedEmployees.removeAt(oldIndex);
                                  _selectedEmployees.insert(newIndex, employee);
                                  // 转换为 Employee 列表后回调
                                  final employeeList = Employee.fromEmployeeTreeModelList(
                                    _selectedEmployees,
                                  );
                                  widget.onChange?.call(employeeList);
                                });
                              },
                            )
                            : ListView(
                              shrinkWrap: true,
                              children:
                                  _selectedEmployees.map((employee) {
                                    return _buildEmployeeItem(employee, itemBottomMargin);
                                  }).toList(),
                            ),
                  ),

                  /// 添加按钮
                  AppButton(
                    type: ButtonType.default_,
                    size: ButtonSize.small,
                    iconData: IconFont.xianxing_tianjia,
                    color: widget.disabled ? AppColors.icon100 : AppColors.icon200,
                    onPressed: widget.disabled ? null : () => showEmployeeSelectorDialog(context),
                  ),
                ],
              ),
            ),
          ],
        )
        : SizedBox();
  }
}
