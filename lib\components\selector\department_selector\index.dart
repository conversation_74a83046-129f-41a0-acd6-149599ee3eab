import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/components/selector/department_selector/department_selector_provider.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/components/selector/department_selector/department_selector_dialog.dart';

/// 部门选择器组件
class DepartmentSelector extends StatefulWidget {
  /// 提交按钮点击回调
  final void Function(List<DepartmentModel>)? onChange;

  /// 默认选中的部门列表(打开弹窗时默认选中)(适用于回显)
  final List<DepartmentModel> defaultCheckedDepartment;

  /// 最多可见行数，超过此数量将显示滚动条
  final int maxVisibleRows;

  /// 是否启用拖拽排序功能
  final bool isDrag;

  /// 是否启用严格模式，父子节点选中状态完全独立，互不影响
  final bool checkStrictly;

  /// 是否显示widget(通过方法打开时设置为false)
  final bool showWidget;

  const DepartmentSelector({
    super.key,
    this.onChange,
    this.defaultCheckedDepartment = const [],
    this.maxVisibleRows = 3,
    this.isDrag = true,
    this.checkStrictly = false,
    this.showWidget = true,
  });

  @override
  State<DepartmentSelector> createState() => DepartmentSelectorState();
}

class DepartmentSelectorState extends State<DepartmentSelector> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  // 维护当前选中的部门列表
  List<DepartmentModel> _selectedDepartments = [];

  @override
  void initState() {
    super.initState();
    // 回显用
    _selectedDepartments = widget.defaultCheckedDepartment;
  }

  @override
  void didUpdateWidget(DepartmentSelector oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 监听 defaultCheckedDepartment 的变化
    if (widget.defaultCheckedDepartment.isEmpty) {
      _selectedDepartments = [];
    }
  }

  /// 打开选择部门弹窗
  void showDepSelectorDialog(BuildContext context) {
    late DepartmentSelectorProvider provider;

    AppDialog.show(
      width: 800,
      height: 600,
      padding: EdgeInsetsGeometry.zero,
      context: context,
      title: '选择部门',
      isDrawer: false,
      child: ChangeNotifierProvider(
        create: (_) {
          provider = DepartmentSelectorProvider();
          // 设置默认选中的部门
          if (widget.defaultCheckedDepartment != null &&
              widget.defaultCheckedDepartment!.isNotEmpty) {
            provider.setDefaultCheckedDepartment(widget.defaultCheckedDepartment!);
          }
          return provider;
        },
        child: DepartmentSelectorDialog(
          departmentSelectorKey: _departmentSelectorKey,
          checkStrictly: widget.checkStrictly,
        ),
      ),
      onConfirm: () {
        // 更新本地选中状态
        setState(() {
          _selectedDepartments = List.from(provider.checkedDepartments);
        });
        widget.onChange?.call(provider.checkedDepartments);
        context.pop();
      },
    );
  }

  /// 计算部门选择器的动态高度
  double _calculateSelectorHeight({
    required List<DepartmentModel> selectedDepartments, // 已选择的部门列表
    required int maxVisibleRows, // 最多可见行数，超过此数量将显示滚动条
    required double itemHeight, // 每个部门项的高度
    required double selectorPadding, // 选择器容器的内边距
    required double itemBottomMargin, // 每个部门项的底部外边距
  }) {
    // 基础高度：单项高度 + 上下内边距
    double baseHeight = itemHeight + selectorPadding * 2;

    // 如果没有选择部门或只选择了1个部门，返回基础高度
    if (selectedDepartments.length <= 1) {
      return baseHeight;
    }

    // 计算实际显示的行数（不超过最大可见行数）
    int actualVisibleRows =
        selectedDepartments.length > maxVisibleRows ? maxVisibleRows : selectedDepartments.length;

    // 计算多行情况下的总高度
    // 公式：单项高度 * 行数 + 上下内边距 * 2 + 每行底部外边距 * 行数
    return itemHeight * actualVisibleRows +
        selectorPadding * 2 +
        itemBottomMargin * actualVisibleRows;
  }

  /// 构建部门项
  Widget _buildDepartmentItem(DepartmentModel department, double itemBottomMargin, {int? index}) {
    return Container(
      key: widget.isDrag && _selectedDepartments.length > 1 ? ValueKey(department.id) : null,
      padding: const EdgeInsets.all(5),
      margin: EdgeInsets.only(right: 10, bottom: itemBottomMargin),
      decoration: BoxDecoration(
        color: context.background200,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 只有在启用拖拽时才显示拖拽图标
          if (widget.isDrag && _selectedDepartments.length > 1 && index != null)
            ReorderableDragStartListener(
              index: index,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Icon(
                  IconFont.xianxing_tuodongpaixu,
                  color: AppColors.icon200,
                  size: AppIconSize.small,
                ),
              ),
            ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 2, left: 8),
              child: Text(department.departmentName, overflow: TextOverflow.ellipsis),
            ),
          ),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedDepartments.removeWhere((d) => d.id == department.id);
                  widget.onChange?.call(_selectedDepartments);
                });
              },
              child: Icon(
                IconFont.mianxing_guanbi,
                color: context.icon200,
                size: AppIconSize.small,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    /// 选择器内边距
    double selectorPadding = 4;

    /// 每项的高度
    double itemHeight = 32;

    /// 每项的底部外边距
    double itemBottomMargin = 5;

    /// 使用提取的方法计算选择器动态高度
    double selectorHeight = _calculateSelectorHeight(
      selectedDepartments: _selectedDepartments,
      maxVisibleRows: widget.maxVisibleRows,
      itemHeight: itemHeight,
      selectorPadding: selectorPadding,
      itemBottomMargin: itemBottomMargin,
    );

    return widget.showWidget!
        ? Container(
          width: double.infinity,
          height: selectorHeight,
          padding: EdgeInsets.all(selectorPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
            border: Border.all(color: context.border300),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              /// 已选部门列表
              Expanded(
                child:
                    _selectedDepartments.isEmpty
                        ? const SizedBox()
                        : widget.isDrag && _selectedDepartments.length > 1
                        ? ReorderableListView(
                          shrinkWrap: true,
                          buildDefaultDragHandles: false,
                          children:
                              _selectedDepartments.asMap().entries.map((entry) {
                                final index = entry.key;
                                final department = entry.value;
                                return _buildDepartmentItem(
                                  department,
                                  itemBottomMargin,
                                  index: index,
                                );
                              }).toList(),
                          onReorder: (oldIndex, newIndex) {
                            setState(() {
                              if (oldIndex < newIndex) {
                                newIndex -= 1;
                              }
                              final department = _selectedDepartments.removeAt(oldIndex);
                              _selectedDepartments.insert(newIndex, department);
                              widget.onChange?.call(_selectedDepartments);
                            });
                          },
                        )
                        : ListView(
                          shrinkWrap: true,
                          children:
                              _selectedDepartments.map((department) {
                                return _buildDepartmentItem(department, itemBottomMargin);
                              }).toList(),
                        ),
              ),

              /// 添加按钮
              AppButton(
                type: ButtonType.default_,
                size: ButtonSize.small,
                iconData: IconFont.xianxing_tianjia,
                color: AppColors.icon200,
                onPressed: () => showDepSelectorDialog(context),
              ),
            ],
          ),
        )
        : SizedBox();
  }
}
